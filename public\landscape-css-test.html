<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基于CSS类名的横屏测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .controls {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 5px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.active {
            background: #28a745;
        }
        
        .status {
            background: #fff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        /* 模拟PhotoView结构 */
        .PhotoView-Portal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0,0,0,0.9);
            z-index: 9999;
            display: none;
        }
        
        .PhotoView-Slider {
            width: 100%;
            height: 100%;
            position: relative;
        }
        
        .PhotoView-Slider__item {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .PhotoView__PhotoWrap {
            width: 80%;
            height: 80%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #fff;
            position: relative;
        }
        
        .PhotoView__PhotoBox {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .PhotoView__Photo {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        
        /* 导入我们的横屏CSS */
        /* 横屏模式基础样式 */
        html.landscape-mode {
            overflow: hidden !important;
        }

        html.landscape-mode body {
            overflow: hidden !important;
        }

        /* PhotoView Portal 横屏变换 */
        html.landscape-mode .PhotoView-Portal {
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            width: 100vh !important;
            height: 100vw !important;
            transform: translate(-50%, -50%) rotate(90deg) !important;
            transform-origin: center center !important;
            z-index: 9999 !important;
            background: #000 !important;
        }

        /* 滑动容器 */
        html.landscape-mode .PhotoView-Slider {
            width: 100% !important;
            height: 100% !important;
            position: relative !important;
        }

        /* 滑动项 */
        html.landscape-mode .PhotoView-Slider__item {
            width: 100% !important;
            height: 100% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        /* 图片包装容器 - 关键修复 */
        html.landscape-mode .PhotoView__PhotoWrap {
            width: 100% !important;
            height: 100% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            position: relative !important;
            overflow: hidden !important;
        }

        /* 图片盒子 */
        html.landscape-mode .PhotoView__PhotoBox {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            width: 100% !important;
            height: 100% !important;
        }

        /* 图片元素 */
        html.landscape-mode .PhotoView__Photo {
            max-width: 100% !important;
            max-height: 100% !important;
            width: auto !important;
            height: auto !important;
            object-fit: contain !important;
            display: block !important;
            margin: 0 auto !important;
        }

        /* 重置所有可能的图片样式 */
        html.landscape-mode .PhotoView-Slider img,
        html.landscape-mode .PhotoView__PhotoWrap img,
        html.landscape-mode .PhotoView__PhotoBox img {
            max-width: 100% !important;
            max-height: 100% !important;
            width: auto !important;
            height: auto !important;
            object-fit: contain !important;
            display: block !important;
            margin: 0 auto !important;
            position: static !important;
            top: auto !important;
            left: auto !important;
            right: auto !important;
            bottom: auto !important;
        }
        
        .debug-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10000;
            max-width: 300px;
        }
    </style>
</head>
<body>
    <div class="status" id="status">
        <h2>基于CSS类名的横屏测试</h2>
        <p>这个测试验证新的横屏解决方案，不使用动态CSS注入，而是通过CSS类名切换。</p>
        <div id="statusText">准备就绪</div>
    </div>
    
    <div class="controls">
        <button class="btn" onclick="toggleViewer()">切换查看器</button>
        <button class="btn" onclick="toggleLandscape()">切换横屏</button>
        <button class="btn" onclick="simulateInlineStyles()">模拟内联样式</button>
        <button class="btn" onclick="resetTest()">重置测试</button>
    </div>
    
    <div class="debug-info" id="debugInfo">
        调试信息加载中...
    </div>
    
    <div class="PhotoView-Portal" id="photoViewer">
        <div class="PhotoView-Slider">
            <div class="PhotoView-Slider__item">
                <div class="PhotoView__PhotoWrap" id="photoWrap">
                    <div class="PhotoView__PhotoBox" id="photoBox">
                        <img src="https://picsum.photos/800/600?random=1" alt="测试图片" class="PhotoView__Photo" id="testImage">
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let isViewerOpen = false;
        let isLandscapeMode = false;
        
        function updateStatus(message) {
            document.getElementById('statusText').textContent = message;
        }
        
        function updateDebugInfo() {
            const photoWrap = document.getElementById('photoWrap');
            const photoBox = document.getElementById('photoBox');
            const img = document.getElementById('testImage');
            
            const wrapStyle = window.getComputedStyle(photoWrap);
            const boxStyle = window.getComputedStyle(photoBox);
            const imgStyle = window.getComputedStyle(img);
            
            const info = `
                <strong>调试信息</strong><br>
                查看器: ${isViewerOpen ? '✅ 开启' : '❌ 关闭'}<br>
                横屏: ${isLandscapeMode ? '✅ 开启' : '❌ 关闭'}<br>
                屏幕: ${window.innerWidth} × ${window.innerHeight}<br>
                <br>
                <strong>PhotoWrap:</strong><br>
                尺寸: ${wrapStyle.width} × ${wrapStyle.height}<br>
                位置: ${wrapStyle.position}<br>
                变换: ${wrapStyle.transform}<br>
                <br>
                <strong>PhotoBox:</strong><br>
                尺寸: ${boxStyle.width} × ${boxStyle.height}<br>
                显示: ${boxStyle.display}<br>
                <br>
                <strong>Image:</strong><br>
                尺寸: ${imgStyle.width} × ${imgStyle.height}<br>
                最大尺寸: ${imgStyle.maxWidth} × ${imgStyle.maxHeight}<br>
                对象适配: ${imgStyle.objectFit}<br>
                显示尺寸: ${img.offsetWidth} × ${img.offsetHeight}
            `;
            
            document.getElementById('debugInfo').innerHTML = info;
        }
        
        function toggleViewer() {
            const viewer = document.getElementById('photoViewer');
            isViewerOpen = !isViewerOpen;
            viewer.style.display = isViewerOpen ? 'block' : 'none';
            updateStatus(isViewerOpen ? '查看器已打开' : '查看器已关闭');
            updateDebugInfo();
        }
        
        function toggleLandscape() {
            isLandscapeMode = !isLandscapeMode;
            if (isLandscapeMode) {
                document.documentElement.classList.add('landscape-mode');
                updateStatus('✅ 横屏模式已启用 - 使用CSS类名切换');
            } else {
                document.documentElement.classList.remove('landscape-mode');
                updateStatus('❌ 横屏模式已禁用');
            }
            updateDebugInfo();
        }
        
        function simulateInlineStyles() {
            const photoWrap = document.getElementById('photoWrap');
            const photoBox = document.getElementById('photoBox');
            const img = document.getElementById('testImage');
            
            // 模拟react-photo-view可能添加的内联样式
            photoWrap.style.transform = 'translate3d(100px, 50px, 0px) scale(1.2)';
            photoWrap.style.left = '20px';
            photoWrap.style.top = '30px';
            photoWrap.style.position = 'absolute';
            
            photoBox.style.transform = 'scale(1.1) rotate(5deg)';
            
            img.style.transform = 'scale(1.3)';
            img.style.position = 'absolute';
            img.style.left = '50px';
            img.style.top = '25px';
            
            updateStatus('⚠️ 已添加内联样式 - 检查横屏模式是否仍然正确显示');
            updateDebugInfo();
        }
        
        function resetTest() {
            const photoWrap = document.getElementById('photoWrap');
            const photoBox = document.getElementById('photoBox');
            const img = document.getElementById('testImage');
            
            // 清除所有内联样式
            photoWrap.removeAttribute('style');
            photoBox.removeAttribute('style');
            img.removeAttribute('style');
            
            updateStatus('🔄 测试已重置');
            updateDebugInfo();
        }
        
        // 初始化
        updateDebugInfo();
        
        // 监听窗口变化
        window.addEventListener('resize', updateDebugInfo);
        
        // 定期更新调试信息
        setInterval(updateDebugInfo, 1000);
    </script>
</body>
</html>
