/**
 * 全屏功能工具函数
 */

// 检查浏览器是否支持全屏API
export const isFullscreenSupported = (): boolean => {
  return !!(
    document.fullscreenEnabled ||
    (document as any).webkitFullscreenEnabled ||
    (document as any).mozFullScreenEnabled ||
    (document as any).msFullscreenEnabled
  );
};

// 检查当前是否处于全屏状态
export const isFullscreen = (): boolean => {
  return !!(
    document.fullscreenElement ||
    (document as any).webkitFullscreenElement ||
    (document as any).mozFullScreenElement ||
    (document as any).msFullscreenElement
  );
};

// 进入全屏
export const requestFullscreen = async (element: HTMLElement = document.documentElement): Promise<void> => {
  try {
    if (!isFullscreenSupported()) {
      throw new Error('浏览器不支持全屏功能');
    }

    if (element.requestFullscreen) {
      await element.requestFullscreen();
    } else if ((element as any).webkitRequestFullscreen) {
      await (element as any).webkitRequestFullscreen();
    } else if ((element as any).mozRequestFullScreen) {
      await (element as any).mozRequestFullScreen();
    } else if ((element as any).msRequestFullscreen) {
      await (element as any).msRequestFullscreen();
    } else {
      throw new Error('无法进入全屏模式');
    }
  } catch (error) {
    console.warn('进入全屏失败:', error);
    throw error;
  }
};

// 退出全屏
export const exitFullscreen = async (): Promise<void> => {
  try {
    if (!isFullscreen()) {
      return;
    }

    if (document.exitFullscreen) {
      await document.exitFullscreen();
    } else if ((document as any).webkitExitFullscreen) {
      await (document as any).webkitExitFullscreen();
    } else if ((document as any).mozCancelFullScreen) {
      await (document as any).mozCancelFullScreen();
    } else if ((document as any).msExitFullscreen) {
      await (document as any).msExitFullscreen();
    } else {
      throw new Error('无法退出全屏模式');
    }
  } catch (error) {
    console.warn('退出全屏失败:', error);
    throw error;
  }
};

// 切换全屏状态
export const toggleFullscreen = async (element?: HTMLElement): Promise<void> => {
  try {
    if (isFullscreen()) {
      await exitFullscreen();
    } else {
      await requestFullscreen(element);
    }
  } catch (error) {
    console.warn('切换全屏状态失败:', error);
    throw error;
  }
};

/**
 * 图片查看器自动播放工具函数
 */

// 自动播放管理器
export class ViewerAutoplay {
  private timer: NodeJS.Timeout | null = null;
  private isPlaying: boolean = false;
  private interval: number = 5000;
  private loop: boolean = true;
  private onNext: (() => void) | null = null;
  private onStop: (() => void) | null = null;

  constructor(options: {
    interval?: number;
    loop?: boolean;
    onNext?: () => void;
    onStop?: () => void;
  } = {}) {
    this.interval = options.interval || 5000;
    this.loop = options.loop !== false;
    this.onNext = options.onNext || null;
    this.onStop = options.onStop || null;
  }

  // 开始播放
  start(): void {
    if (this.isPlaying) return;
    
    this.isPlaying = true;
    this.timer = setInterval(() => {
      if (this.onNext) {
        this.onNext();
      }
    }, this.interval);
  }

  // 停止播放
  stop(): void {
    if (!this.isPlaying) return;
    
    this.isPlaying = false;
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    
    if (this.onStop) {
      this.onStop();
    }
  }

  // 暂停播放
  pause(): void {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    this.isPlaying = false;
  }

  // 恢复播放
  resume(): void {
    if (!this.isPlaying) {
      this.start();
    }
  }

  // 切换播放状态
  toggle(): void {
    if (this.isPlaying) {
      this.pause();
    } else {
      this.start();
    }
  }

  // 获取播放状态
  getPlayingState(): boolean {
    return this.isPlaying;
  }

  // 设置播放间隔
  setInterval(interval: number): void {
    this.interval = interval;
    if (this.isPlaying) {
      this.stop();
      this.start();
    }
  }

  // 设置循环模式
  setLoop(loop: boolean): void {
    this.loop = loop;
  }

  // 销毁
  destroy(): void {
    this.stop();
    this.onNext = null;
    this.onStop = null;
  }
}

/**
 * 设备检测工具函数
 */

// 检查是否为移动设备
export const isMobileDevice = (): boolean => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

// 检查是否为触摸设备
export const isTouchDevice = (): boolean => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

// 检查是否为iOS设备
export const isIOSDevice = (): boolean => {
  return /iPad|iPhone|iPod/.test(navigator.userAgent);
};

// 检查是否为Android设备
export const isAndroidDevice = (): boolean => {
  return /Android/.test(navigator.userAgent);
};

/**
 * 屏幕方向控制工具函数
 * 支持原生API和H5模拟两种实现方式
 */

// 横屏模式类型
export enum LandscapeMode {
  NATIVE = 'native',     // 原生屏幕方向API
  SIMULATE = 'simulate', // H5模拟横屏
  DISABLED = 'disabled'  // 不支持横屏
}

// 检查是否支持原生屏幕方向API
export const isNativeOrientationSupported = (): boolean => {
  return !!(
    screen.orientation ||
    (screen as any).mozOrientation ||
    (screen as any).msOrientation
  );
};

// 检测当前环境支持的横屏模式
export const detectLandscapeMode = (): LandscapeMode => {
  // 检测是否在受限环境中（如某些App内WebView）
  const userAgent = navigator.userAgent.toLowerCase();
  const isInApp = /micromessenger|alipay|qq|weibo|douyin|tiktok/.test(userAgent);
  const isInWebView = /webview|wv/.test(userAgent);

  // 在App内或WebView中，直接使用H5模拟
  if (isInApp || isInWebView) {
    return LandscapeMode.SIMULATE;
  }

  // 检测原生API支持（更严格的检测）
  if (isNativeOrientationSupported()) {
    try {
      // 检查API是否真正可用
      if (screen.orientation && (screen.orientation as any).lock) {
        // 进一步检测：某些设备虽然有API但不允许调用
        // 通过检查当前环境来判断
        const isSecureContext = window.isSecureContext !== false;
        const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
        const isFullscreen = document.fullscreenElement !== null;

        // 在安全上下文且不是受限环境中才使用原生API
        if (isSecureContext && !isStandalone && !isFullscreen) {
          return LandscapeMode.NATIVE;
        }
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('原生屏幕方向API检测失败:', error);
      }
    }
  }

  // 默认使用H5模拟模式（最兼容的方案）
  return LandscapeMode.SIMULATE;
};

// 检查是否支持横屏功能（原生或模拟）
export const isOrientationSupported = (): boolean => {
  return detectLandscapeMode() !== LandscapeMode.DISABLED;
};

/**
 * H5模拟横屏状态管理
 */
class H5LandscapeManager {
  private isSimulateLandscape: boolean = false;
  private originalBodyStyle: string = '';
  private callbacks: Array<(isLandscape: boolean) => void> = [];

  // 检查是否处于H5模拟横屏状态
  isInSimulateLandscape(): boolean {
    return this.isSimulateLandscape;
  }

  // 添加状态变化监听器
  addChangeListener(callback: (isLandscape: boolean) => void): void {
    this.callbacks.push(callback);
  }

  // 移除状态变化监听器
  removeChangeListener(callback: (isLandscape: boolean) => void): void {
    const index = this.callbacks.indexOf(callback);
    if (index > -1) {
      this.callbacks.splice(index, 1);
    }
  }

  // 通知状态变化
  private notifyChange(): void {
    this.callbacks.forEach(callback => {
      try {
        callback(this.isSimulateLandscape);
      } catch (error) {
        console.warn('横屏状态变化回调执行失败:', error);
      }
    });
  }

  // 进入H5模拟横屏模式
  async enterSimulateLandscape(): Promise<void> {
    if (this.isSimulateLandscape) return;

    try {
      // 保存原始样式
      this.originalBodyStyle = document.body.style.cssText;

      // 等待PhotoView渲染完成
      await this.waitForPhotoView();

      // 应用横屏样式（不创建额外容器）
      this.applyLandscapeStyles();

      this.isSimulateLandscape = true;
      this.notifyChange();

      if (process.env.NODE_ENV === 'development') {
        console.log('进入H5模拟横屏模式');
      }
    } catch (error) {
      console.error('进入H5模拟横屏失败:', error);
      throw error;
    }
  }

  // 等待PhotoView渲染完成
  private async waitForPhotoView(): Promise<void> {
    return new Promise((resolve) => {
      const checkPhotoView = () => {
        const photoView = document.querySelector('.PhotoView-Portal');
        if (photoView) {
          resolve();
        } else {
          // 如果PhotoView还没渲染，等待一下再检查
          setTimeout(checkPhotoView, 50);
        }
      };
      checkPhotoView();
    });
  }

  // 退出H5模拟横屏模式
  async exitSimulateLandscape(): Promise<void> {
    if (!this.isSimulateLandscape) return;

    try {
      // 恢复原始样式
      this.restoreOriginalStyles();

      this.isSimulateLandscape = false;
      this.notifyChange();

      if (process.env.NODE_ENV === 'development') {
        console.log('退出H5模拟横屏模式');
      }
    } catch (error) {
      console.error('退出H5模拟横屏失败:', error);
      throw error;
    }
  }



  // 应用横屏样式
  private applyLandscapeStyles(): void {
    // 禁用页面滚动
    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';

    // 添加横屏样式类
    document.body.classList.add('h5-landscape-mode');

    // 动态添加CSS样式
    this.injectLandscapeCSS();
  }

  // 恢复原始样式
  private restoreOriginalStyles(): void {
    // 恢复body样式
    document.body.style.cssText = this.originalBodyStyle;
    document.documentElement.style.overflow = '';

    // 移除横屏样式类
    document.body.classList.remove('h5-landscape-mode');

    // 移除注入的CSS
    this.removeLandscapeCSS();
  }

  // 注入横屏CSS样式
  private injectLandscapeCSS(): void {
    const styleId = 'h5-landscape-styles';
    if (document.getElementById(styleId)) return;

    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      /* H5模拟横屏样式 */
      .h5-landscape-mode .PhotoView-Portal {
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        width: 100vh !important;
        height: 100vw !important;
        transform: translate(-50%, -50%) rotate(90deg) !important;
        transform-origin: center center !important;
        z-index: 9999 !important;
        background: #000 !important;
      }

      .h5-landscape-mode .PhotoView-Slider {
        width: 100vh !important;
        height: 100vw !important;
      }

      .h5-landscape-mode .PhotoView-Slider__item {
        width: 100vh !important;
        height: 100vw !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
      }

      .h5-landscape-mode .PhotoView-Slider__item img {
        max-width: 100% !important;
        max-height: 100% !important;
        object-fit: contain !important;
      }

      /* 工具栏在横屏模式下的调整 */
      .h5-landscape-mode .PhotoView-Slider__toolbarIcon {
        color: white !important;
      }

      /* 确保背景是黑色 */
      .h5-landscape-mode .PhotoView-Slider__backdrop {
        background: #000 !important;
      }
    `;

    document.head.appendChild(style);
  }

  // 移除注入的CSS样式
  private removeLandscapeCSS(): void {
    const style = document.getElementById('h5-landscape-styles');
    if (style && style.parentNode) {
      style.parentNode.removeChild(style);
    }
  }


}

// 全局H5横屏管理器实例
const h5LandscapeManager = new H5LandscapeManager();

// 获取当前屏幕方向
export const getCurrentOrientation = (): string => {
  // 优先检查H5模拟状态
  if (h5LandscapeManager.isInSimulateLandscape()) {
    return 'landscape-primary';
  }

  // 检查原生API
  if (screen.orientation) {
    return screen.orientation.type;
  }

  // 回退到窗口尺寸判断
  return window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
};

// 检查是否为横屏
export const isLandscape = (): boolean => {
  // 优先检查H5模拟状态
  if (h5LandscapeManager.isInSimulateLandscape()) {
    return true;
  }

  const orientation = getCurrentOrientation();
  return orientation.includes('landscape') || window.innerWidth > window.innerHeight;
};

// 请求横屏（支持原生和模拟两种方式，带智能降级）
export const requestLandscape = async (): Promise<void> => {
  let mode = detectLandscapeMode();

  try {
    switch (mode) {
      case LandscapeMode.NATIVE:
        try {
          await requestNativeLandscape();
          return;
        } catch (nativeError) {
          // 原生API失败，自动降级到H5模拟
          if (process.env.NODE_ENV === 'development') {
            console.warn('原生API失败，降级到H5模拟:', nativeError);
          }
          mode = LandscapeMode.SIMULATE;
          await h5LandscapeManager.enterSimulateLandscape();
          return;
        }

      case LandscapeMode.SIMULATE:
        await h5LandscapeManager.enterSimulateLandscape();
        return;

      default:
        throw new Error('当前环境不支持横屏功能');
    }
  } catch (error) {
    // 只在开发环境输出错误日志
    if (process.env.NODE_ENV === 'development') {
      console.warn('请求横屏失败:', error);
    }
    throw error;
  }
};

// 原生API请求横屏（带降级处理）
const requestNativeLandscape = async (): Promise<void> => {
  const errors: string[] = [];

  // 尝试标准API
  if (screen.orientation && (screen.orientation as any).lock) {
    try {
      await (screen.orientation as any).lock('landscape');
      return;
    } catch (error) {
      errors.push(`screen.orientation.lock: ${error}`);
    }
  }

  // 尝试带前缀的API
  const apis = [
    { name: 'lockOrientation', fn: (screen as any).lockOrientation },
    { name: 'mozLockOrientation', fn: (screen as any).mozLockOrientation },
    { name: 'msLockOrientation', fn: (screen as any).msLockOrientation }
  ];

  for (const api of apis) {
    if (api.fn) {
      try {
        await api.fn.call(screen, 'landscape');
        return;
      } catch (error) {
        errors.push(`${api.name}: ${error}`);
      }
    }
  }

  // 所有原生API都失败，抛出错误以触发降级
  throw new Error(`原生API不可用: ${errors.join(', ')}`);
};

// 请求竖屏（支持原生和模拟两种方式，带智能降级）
export const requestPortrait = async (): Promise<void> => {
  let mode = detectLandscapeMode();

  // 如果当前是H5模拟横屏状态，直接退出模拟
  if (h5LandscapeManager.isInSimulateLandscape()) {
    await h5LandscapeManager.exitSimulateLandscape();
    return;
  }

  try {
    switch (mode) {
      case LandscapeMode.NATIVE:
        try {
          await requestNativePortrait();
          return;
        } catch (nativeError) {
          // 原生API失败，但竖屏通常不需要降级处理
          if (process.env.NODE_ENV === 'development') {
            console.warn('原生竖屏API失败:', nativeError);
          }
          // 对于竖屏，如果原生API失败，通常不需要特殊处理
          return;
        }

      case LandscapeMode.SIMULATE:
        await h5LandscapeManager.exitSimulateLandscape();
        return;

      default:
        // 不支持的环境，静默处理
        return;
    }
  } catch (error) {
    // 只在开发环境输出错误日志
    if (process.env.NODE_ENV === 'development') {
      console.warn('请求竖屏失败:', error);
    }
    // 竖屏失败通常不需要抛出错误
  }
};

// 原生API请求竖屏（带降级处理）
const requestNativePortrait = async (): Promise<void> => {
  const errors: string[] = [];

  // 尝试标准API
  if (screen.orientation && (screen.orientation as any).lock) {
    try {
      await (screen.orientation as any).lock('portrait');
      return;
    } catch (error) {
      errors.push(`screen.orientation.lock: ${error}`);
    }
  }

  // 尝试带前缀的API
  const apis = [
    { name: 'lockOrientation', fn: (screen as any).lockOrientation },
    { name: 'mozLockOrientation', fn: (screen as any).mozLockOrientation },
    { name: 'msLockOrientation', fn: (screen as any).msLockOrientation }
  ];

  for (const api of apis) {
    if (api.fn) {
      try {
        await api.fn.call(screen, 'portrait');
        return;
      } catch (error) {
        errors.push(`${api.name}: ${error}`);
      }
    }
  }

  // 所有原生API都失败，抛出错误以触发降级
  throw new Error(`原生API不可用: ${errors.join(', ')}`);
};

// 解锁屏幕方向
export const unlockOrientation = async (): Promise<void> => {
  const mode = detectLandscapeMode();

  try {
    switch (mode) {
      case LandscapeMode.NATIVE:
        await unlockNativeOrientation();
        break;

      case LandscapeMode.SIMULATE:
        await h5LandscapeManager.exitSimulateLandscape();
        break;

      default:
        // 不支持的环境，静默处理
        break;
    }
  } catch (error) {
    console.warn('解锁屏幕方向失败:', error);
    throw error;
  }
};

// 原生API解锁屏幕方向
const unlockNativeOrientation = async (): Promise<void> => {
  if (screen.orientation && (screen.orientation as any).unlock) {
    await (screen.orientation as any).unlock();
  } else if ((screen as any).unlockOrientation) {
    await (screen as any).unlockOrientation();
  } else if ((screen as any).mozUnlockOrientation) {
    await (screen as any).mozUnlockOrientation();
  } else if ((screen as any).msUnlockOrientation) {
    await (screen as any).msUnlockOrientation();
  }
};

// 切换屏幕方向（智能选择实现方式）
export const toggleOrientation = async (): Promise<void> => {
  try {
    if (isLandscape()) {
      await requestPortrait();
    } else {
      await requestLandscape();
    }
  } catch (error) {
    console.warn('切换屏幕方向失败:', error);
    throw error;
  }
};

// 获取当前横屏模式
export const getCurrentLandscapeMode = (): LandscapeMode => {
  return detectLandscapeMode();
};

// 添加横屏状态变化监听器
export const addLandscapeChangeListener = (callback: (isLandscape: boolean) => void): void => {
  h5LandscapeManager.addChangeListener(callback);
};

// 移除横屏状态变化监听器
export const removeLandscapeChangeListener = (callback: (isLandscape: boolean) => void): void => {
  h5LandscapeManager.removeChangeListener(callback);
};

// 导出H5横屏管理器（供高级用法使用）
export { h5LandscapeManager };
