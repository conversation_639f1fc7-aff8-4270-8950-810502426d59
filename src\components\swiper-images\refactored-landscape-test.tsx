import React, { useState, useEffect } from 'react';
import SwiperImages from './index';
import { simpleLandscapeManager } from './landscape-manager';
import styles from './demo.module.scss';

const RefactoredLandscapeTest: React.FC = () => {
  const [images] = useState([
    {
      src: 'https://picsum.photos/800/600?random=1',
      alt: '测试图片1'
    },
    {
      src: 'https://picsum.photos/600/800?random=2',
      alt: '测试图片2'
    },
    {
      src: 'https://picsum.photos/1200/800?random=3',
      alt: '测试图片3'
    },
    {
      src: 'https://picsum.photos/800/1200?random=4',
      alt: '测试图片4'
    }
  ]);

  const [isLandscape, setIsLandscape] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);

  useEffect(() => {
    // 监听横屏状态变化
    const handleLandscapeChange = (isLandscapeMode: boolean) => {
      setIsLandscape(isLandscapeMode);
      addTestResult(`横屏状态变化: ${isLandscapeMode ? '横屏' : '竖屏'}`);
    };

    simpleLandscapeManager.addListener(handleLandscapeChange);

    return () => {
      simpleLandscapeManager.removeListener(handleLandscapeChange);
    };
  }, []);

  const addTestResult = (result: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, `[${timestamp}] ${result}`]);
  };

  const testLandscapeToggle = () => {
    addTestResult('手动切换横屏模式');
    simpleLandscapeManager.toggleLandscape();
  };

  const testEnterLandscape = () => {
    addTestResult('手动进入横屏模式');
    simpleLandscapeManager.enterLandscape();
  };

  const testExitLandscape = () => {
    addTestResult('手动退出横屏模式');
    simpleLandscapeManager.exitLandscape();
  };

  const clearTestResults = () => {
    setTestResults([]);
  };

  const checkCSSClass = () => {
    const hasClass = document.documentElement.classList.contains('landscape-mode');
    addTestResult(`HTML根元素横屏CSS类检查: ${hasClass ? '存在' : '不存在'}`);
  };

  return (
    <div className={styles.demoContainer}>
      <h1>🔧 重构后横屏功能测试</h1>
      
      <div className={styles.section}>
        <h2>📋 重构说明</h2>
        <div className={styles.instructions}>
          <p><strong>重构目标：</strong></p>
          <ul>
            <li>✅ 简化横屏按钮的点击逻辑</li>
            <li>✅ 通过HTML根节点统一管理横屏模式状态</li>
            <li>✅ 将横屏相关的样式和逻辑集中管理</li>
            <li>✅ 确保在退出全屏时能够正确恢复到竖屏模式</li>
            <li>✅ 保持现有功能不变的前提下，优化代码结构</li>
          </ul>
          
          <p><strong>核心改进：</strong></p>
          <ul>
            <li>🎯 <strong>状态驱动</strong>：通过CSS类切换实现横屏效果</li>
            <li>🏗️ <strong>集中管理</strong>：SimpleLandscapeManager统一管理状态</li>
            <li>🔄 <strong>自动恢复</strong>：退出全屏时自动恢复竖屏模式</li>
            <li>⚡ <strong>性能优化</strong>：避免复杂的API调用和异步操作</li>
            <li>🧹 <strong>代码简化</strong>：移除冗余的模式检测和降级逻辑</li>
          </ul>
        </div>
      </div>

      <div className={styles.section}>
        <h2>🎮 手动测试控制</h2>
        <div className={styles.controls}>
          <button onClick={testLandscapeToggle} className="test-button">
            切换横屏模式
          </button>
          <button onClick={testEnterLandscape} className="test-button">
            进入横屏模式
          </button>
          <button onClick={testExitLandscape} className="test-button">
            退出横屏模式
          </button>
          <button onClick={checkCSSClass} className="test-button">
            检查CSS类状态
          </button>
          <button onClick={clearTestResults} className="test-button">
            清空测试结果
          </button>
        </div>
        
        <div className={styles.instructions}>
          <p><strong>当前状态：</strong></p>
          <ul>
            <li>横屏模式：{isLandscape ? '✅ 已开启' : '❌ 已关闭'}</li>
            <li>HTML类名：{document.documentElement.classList.contains('landscape-mode') ? '✅ landscape-mode' : '❌ 无'}</li>
          </ul>
        </div>
      </div>

      <div className={styles.section}>
        <h2>🖼️ 图片查看器测试</h2>
        <p>点击任意图片，然后使用工具栏中的横屏按钮测试功能：</p>
        <SwiperImages
          images={images}
          height="300px"
          showToolbar={true}
          showViewerPlayButton={true}
          showRotateButton={true}
          showScaleButtons={true}
          showFullscreenButton={true}
          showLandscapeButton={true}
          autoplay={false}
          interval={3000}
          loop={true}
          showIndicator={true}
        />
      </div>

      <div className={styles.section}>
        <h2>📊 测试结果日志</h2>
        <div style={{
          background: '#f8f9fa',
          border: '1px solid #e9ecef',
          borderRadius: '4px',
          padding: '15px',
          maxHeight: '300px',
          overflowY: 'auto',
          fontFamily: 'monospace',
          fontSize: '12px'
        }}>
          {testResults.length === 0 ? (
            <p style={{ color: '#6c757d', margin: 0 }}>暂无测试结果...</p>
          ) : (
            testResults.map((result, index) => (
              <div key={index} style={{ marginBottom: '5px' }}>
                {result}
              </div>
            ))
          )}
        </div>
      </div>

      <div className={styles.section}>
        <h2>🔍 技术实现对比</h2>
        <div className={styles.instructions}>
          <h3>重构前（复杂实现）：</h3>
          <ul>
            <li>❌ 多种横屏模式检测（原生API、H5模拟）</li>
            <li>❌ 复杂的异步操作和错误处理</li>
            <li>❌ 分散的状态管理和样式注入</li>
            <li>❌ 多层嵌套的条件判断</li>
            <li>❌ 冗余的兼容性检测代码</li>
          </ul>
          
          <h3>重构后（简化实现）：</h3>
          <ul>
            <li>✅ 单一的CSS类切换机制</li>
            <li>✅ 同步操作，无需异步处理</li>
            <li>✅ 集中的状态管理器</li>
            <li>✅ 简洁的切换逻辑</li>
            <li>✅ 自动的全屏退出处理</li>
          </ul>
          
          <h3>核心代码对比：</h3>
          <div style={{ background: '#f8f9fa', padding: '10px', borderRadius: '4px', marginTop: '10px' }}>
            <p><strong>重构前：</strong></p>
            <code style={{ fontSize: '11px' }}>
              await toggleOrientation() // 复杂的异步API调用
            </code>
            
            <p style={{ marginTop: '15px' }}><strong>重构后：</strong></p>
            <code style={{ fontSize: '11px' }}>
              simpleLandscapeManager.toggleLandscape() // 简单的同步调用
            </code>
          </div>
        </div>
      </div>

      <div className={styles.section}>
        <h2>✅ 验证清单</h2>
        <div className={styles.instructions}>
          <p><strong>请验证以下功能：</strong></p>
          <ol>
            <li>点击图片进入查看器</li>
            <li>点击横屏按钮，验证：
              <ul>
                <li>图片正确旋转90度显示</li>
                <li>工具栏位置正确（右上角）</li>
                <li>所有按钮可正常点击</li>
                <li>导航按钮位置正确</li>
                <li>关闭按钮位置正确</li>
              </ul>
            </li>
            <li>再次点击横屏按钮退出横屏模式</li>
            <li>测试全屏功能，验证退出全屏时自动恢复竖屏</li>
            <li>使用手动测试按钮验证状态管理</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default RefactoredLandscapeTest;
