<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SwiperImages 横屏功能测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        
        /* 模拟PhotoView结构的样式 */
        .photo-view-simulation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: #000;
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .photo-view-slider {
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .photo-view-item {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .photo-view-wrap {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .test-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        
        .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .landscape-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 123, 255, 0.8);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .nav-btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .nav-btn.left {
            left: 20px;
        }
        
        .nav-btn.right {
            right: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 SwiperImages 横屏功能测试</h1>
        
        <div class="section">
            <h2>📋 测试说明</h2>
            <div class="highlight">
                <p><strong>测试目标：</strong>验证重构后的横屏功能在图片切换时的表现</p>
                <p><strong>问题描述：</strong>横屏模式下切换图片时，图片尺寸计算不正确</p>
                <p><strong>修复方案：</strong>优化CSS选择器，确保图片容器正确适配横屏模式</p>
            </div>
        </div>

        <div class="section">
            <h2>🎮 模拟测试</h2>
            <p>点击下面的按钮模拟PhotoView图片查看器，然后测试横屏功能：</p>
            <button class="test-button" onclick="openPhotoView()">打开图片查看器</button>
            <button class="test-button" onclick="toggleLandscape()">切换横屏模式</button>
            <button class="test-button" onclick="checkStatus()">检查状态</button>
            
            <div id="status-display"></div>
        </div>

        <div class="section">
            <h2>🔍 实际测试步骤</h2>
            <div class="status info">
                <p><strong>在React应用中测试：</strong></p>
                <ol>
                    <li>访问 <code>http://localhost:8777/</code></li>
                    <li>找到使用SwiperImages组件的页面</li>
                    <li>点击任意图片进入PhotoView查看器</li>
                    <li>点击横屏按钮进入横屏模式</li>
                    <li>使用左右箭头或滑动切换图片</li>
                    <li>观察图片是否正确显示和缩放</li>
                    <li>再次点击横屏按钮退出横屏模式</li>
                    <li>验证所有功能正常工作</li>
                </ol>
            </div>
        </div>

        <div class="section">
            <h2>🐛 问题分析</h2>
            <div class="status warning">
                <p><strong>可能的问题原因：</strong></p>
                <ul>
                    <li>PhotoView内部结构的CSS选择器不够精确</li>
                    <li>图片容器的尺寸计算在横屏模式下有误</li>
                    <li>CSS变换和尺寸设置冲突</li>
                    <li>图片切换时状态没有正确更新</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>✅ 修复验证</h2>
            <div class="status success">
                <p><strong>修复内容：</strong></p>
                <ul>
                    <li>✅ 添加了 <code>.PhotoView-PhotoWrap</code> 选择器</li>
                    <li>✅ 添加了 <code>.PhotoView-Slider__BannerWrap</code> 选择器</li>
                    <li>✅ 添加了 <code>.PhotoView-Slider__BannerBox</code> 选择器</li>
                    <li>✅ 重置了所有可能的变换样式</li>
                    <li>✅ 确保图片容器使用正确的尺寸</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📱 测试图片</h2>
            <p>以下是一些测试用的图片链接，可以在实际应用中使用：</p>
            <ul>
                <li>横向图片：<code>https://picsum.photos/800/600</code></li>
                <li>竖向图片：<code>https://picsum.photos/600/800</code></li>
                <li>正方形图片：<code>https://picsum.photos/600/600</code></li>
                <li>超宽图片：<code>https://picsum.photos/1200/400</code></li>
            </ul>
        </div>
    </div>

    <!-- 模拟PhotoView结构 -->
    <div id="photo-view-simulation" class="photo-view-simulation">
        <div class="photo-view-slider PhotoView-Slider">
            <div class="photo-view-item PhotoView-Slider__item">
                <div class="photo-view-wrap PhotoView-PhotoWrap">
                    <img id="test-image" class="test-image" src="https://picsum.photos/800/600?random=1" alt="测试图片">
                </div>
            </div>
            <button class="close-btn" onclick="closePhotoView()">×</button>
            <button class="landscape-btn" onclick="toggleLandscape()">📱 横屏</button>
            <button class="nav-btn left" onclick="prevImage()">‹</button>
            <button class="nav-btn right" onclick="nextImage()">›</button>
        </div>
    </div>

    <script>
        let currentImageIndex = 0;
        const testImages = [
            'https://picsum.photos/800/600?random=1',
            'https://picsum.photos/600/800?random=2',
            'https://picsum.photos/1200/400?random=3',
            'https://picsum.photos/600/600?random=4'
        ];

        function openPhotoView() {
            const simulation = document.getElementById('photo-view-simulation');
            simulation.style.display = 'flex';
            updateStatus('✅ 图片查看器已打开');
        }

        function closePhotoView() {
            const simulation = document.getElementById('photo-view-simulation');
            simulation.style.display = 'none';
            // 退出横屏模式
            document.documentElement.classList.remove('landscape-mode');
            updateStatus('📱 图片查看器已关闭，横屏模式已退出');
        }

        function toggleLandscape() {
            const isLandscape = document.documentElement.classList.contains('landscape-mode');

            if (isLandscape) {
                document.documentElement.classList.remove('landscape-mode');
                updateStatus('📱 已退出横屏模式 - 图片应该恢复正常显示');
            } else {
                document.documentElement.classList.add('landscape-mode');
                updateStatus('📱 已进入横屏模式 - 图片应该适配横屏布局');

                // 模拟PhotoView添加内联样式的问题
                setTimeout(() => {
                    const photoWrap = document.querySelector('.PhotoView__PhotoWrap');
                    const img = document.getElementById('test-image');

                    if (photoWrap) {
                        photoWrap.style.transform = 'translate3d(100px, 50px, 0px) scale(1.2)';
                        photoWrap.style.left = '20px';
                        photoWrap.style.top = '30px';
                        updateStatus('⚠️ 模拟了内联样式问题 - 检查图片是否仍然正确显示');
                    }
                }, 500);
            }
        }

        function prevImage() {
            currentImageIndex = (currentImageIndex - 1 + testImages.length) % testImages.length;
            updateImage();
            updateStatus(`⬅️ 切换到第 ${currentImageIndex + 1} 张图片`);
        }

        function nextImage() {
            currentImageIndex = (currentImageIndex + 1) % testImages.length;
            updateImage();
            updateStatus(`➡️ 切换到第 ${currentImageIndex + 1} 张图片`);
        }

        function updateImage() {
            const img = document.getElementById('test-image');
            img.src = testImages[currentImageIndex];
        }

        function checkStatus() {
            const isLandscape = document.documentElement.classList.contains('landscape-mode');
            const isOpen = document.getElementById('photo-view-simulation').style.display === 'flex';
            const img = document.getElementById('test-image');

            let imgInfo = '';
            if (img) {
                const computedStyle = window.getComputedStyle(img);
                imgInfo = `<br>• 图片尺寸：${img.naturalWidth} × ${img.naturalHeight}<br>• 显示尺寸：${img.offsetWidth} × ${img.offsetHeight}<br>• CSS变换：${computedStyle.transform}<br>• CSS位置：${computedStyle.position}`;
            }

            updateStatus(`
                📊 当前状态：<br>
                • 查看器：${isOpen ? '✅ 已打开' : '❌ 已关闭'}<br>
                • 横屏模式：${isLandscape ? '✅ 已开启' : '❌ 已关闭'}<br>
                • 当前图片：第 ${currentImageIndex + 1} 张<br>
                • 屏幕尺寸：${window.innerWidth} × ${window.innerHeight}${imgInfo}
            `);
        }

        function updateStatus(message) {
            const statusDisplay = document.getElementById('status-display');
            const timestamp = new Date().toLocaleTimeString();
            statusDisplay.innerHTML = `
                <div class="status info">
                    <strong>[${timestamp}]</strong> ${message}
                </div>
            `;
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            updateStatus('🚀 测试页面已加载，可以开始测试');
            
            // 监听键盘事件
            document.addEventListener('keydown', function(e) {
                const isOpen = document.getElementById('photo-view-simulation').style.display === 'flex';
                if (!isOpen) return;
                
                switch(e.key) {
                    case 'ArrowLeft':
                        prevImage();
                        break;
                    case 'ArrowRight':
                        nextImage();
                        break;
                    case 'Escape':
                        closePhotoView();
                        break;
                    case 'l':
                    case 'L':
                        toggleLandscape();
                        break;
                }
            });
        });
    </script>
</body>
</html>
