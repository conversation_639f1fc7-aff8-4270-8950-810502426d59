import React, { useState, useEffect } from 'react';
import { Button, Space, Toast, Card, Tag } from 'antd-mobile';
import SwiperImages, { ImageItem } from './index';
import { 
  getCurrentLandscapeMode, 
  LandscapeMode, 
  isLandscape, 
  getCurrentOrientation,
  addLandscapeChangeListener,
  removeLandscapeChangeListener,
  toggleOrientation
} from './utils';
import styles from './demo.module.scss';

// 测试图片 - 包含横向和竖向图片
const testImages: ImageItem[] = [
  {
    src: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1200&h=800&fit=crop',
    thumbnail: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop',
    alt: '横向风景图片1 - 适合横屏查看',
  },
  {
    src: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=1200&h=800&fit=crop',
    thumbnail: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=400&h=300&fit=crop',
    alt: '横向风景图片2 - 适合横屏查看',
  },
  {
    src: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=1200&fit=crop',
    thumbnail: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=300&h=400&fit=crop',
    alt: '竖向风景图片 - 适合竖屏查看',
  },
  {
    src: 'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=1200&h=800&fit=crop',
    thumbnail: 'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=400&h=300&fit=crop',
    alt: '横向海景图片 - 适合横屏查看',
  },
];

const H5LandscapeDemo: React.FC = () => {
  const [landscapeMode, setLandscapeMode] = useState<LandscapeMode>(getCurrentLandscapeMode());
  const [isCurrentLandscape, setIsCurrentLandscape] = useState(isLandscape());
  const [currentOrientation, setCurrentOrientation] = useState(getCurrentOrientation());
  const [userAgent] = useState(navigator.userAgent);

  // 监听横屏状态变化
  useEffect(() => {
    const handleLandscapeChange = (isLandscapeNow: boolean) => {
      setIsCurrentLandscape(isLandscapeNow);
      setLandscapeMode(getCurrentLandscapeMode());
      setCurrentOrientation(getCurrentOrientation());
    };

    const handleOrientationChange = () => {
      setIsCurrentLandscape(isLandscape());
      setLandscapeMode(getCurrentLandscapeMode());
      setCurrentOrientation(getCurrentOrientation());
    };

    // 添加监听器
    addLandscapeChangeListener(handleLandscapeChange);
    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('resize', handleOrientationChange);

    return () => {
      removeLandscapeChangeListener(handleLandscapeChange);
      window.removeEventListener('orientationchange', handleOrientationChange);
      window.removeEventListener('resize', handleOrientationChange);
    };
  }, []);

  const handleImageClick = (_index: number, image: ImageItem) => {
    Toast.show({
      content: `点击了图片: ${image.alt}`,
      duration: 1500,
    });
  };

  const handleManualToggle = async () => {
    try {
      await toggleOrientation();
      Toast.show({
        content: `切换到${isCurrentLandscape ? '竖屏' : '横屏'}模式`,
        duration: 2000,
      });
    } catch (error) {
      Toast.show({
        content: `切换失败: ${error}`,
        duration: 3000,
      });
    }
  };

  const getModeDescription = (mode: LandscapeMode): string => {
    switch (mode) {
      case LandscapeMode.NATIVE:
        return '使用浏览器原生屏幕方向API，真正改变设备屏幕方向';
      case LandscapeMode.SIMULATE:
        return '使用H5技术模拟横屏效果，通过CSS transform实现视觉横屏';
      case LandscapeMode.DISABLED:
        return '当前环境不支持横屏功能';
      default:
        return '未知模式';
    }
  };

  const getModeColor = (mode: LandscapeMode): string => {
    switch (mode) {
      case LandscapeMode.NATIVE:
        return 'success';
      case LandscapeMode.SIMULATE:
        return 'warning';
      case LandscapeMode.DISABLED:
        return 'danger';
      default:
        return 'default';
    }
  };

  const isInApp = /micromessenger|alipay|qq|weibo|douyin|tiktok/i.test(userAgent);
  const isInWebView = /webview|wv/i.test(userAgent);

  return (
    <div className={styles.demoContainer}>
      <h1>H5横屏功能演示</h1>

      {/* 环境检测 */}
      <section className={styles.section}>
        <h2>环境检测</h2>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <Card>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <strong>横屏模式：</strong>
              <Tag color={getModeColor(landscapeMode)}>
                {landscapeMode.toUpperCase()}
              </Tag>
            </div>
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
              {getModeDescription(landscapeMode)}
            </div>
          </Card>

          <Card>
            <div><strong>当前方向：</strong> {isCurrentLandscape ? '横屏' : '竖屏'}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              屏幕方向: {currentOrientation}
            </div>
          </Card>

          <Card>
            <div><strong>屏幕尺寸：</strong> {window.innerWidth} × {window.innerHeight}</div>
          </Card>

          <Card>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <strong>运行环境：</strong>
              {isInApp && <Tag color="primary">App内浏览器</Tag>}
              {isInWebView && <Tag color="warning">WebView</Tag>}
              {!isInApp && !isInWebView && <Tag color="success">独立浏览器</Tag>}
            </div>
          </Card>
        </div>
      </section>

      {/* 手动测试 */}
      <section className={styles.section}>
        <h2>手动测试</h2>
        <Space>
          <Button 
            color="primary" 
            onClick={handleManualToggle}
            disabled={landscapeMode === LandscapeMode.DISABLED}
          >
            {isCurrentLandscape ? '切换到竖屏' : '切换到横屏'}
          </Button>
        </Space>
        <div style={{ fontSize: '12px', color: '#666', marginTop: '8px' }}>
          {landscapeMode === LandscapeMode.SIMULATE && 
            '💡 当前使用H5模拟模式，会通过CSS变换实现横屏效果'
          }
          {landscapeMode === LandscapeMode.NATIVE && 
            '✨ 当前使用原生API，会真正改变设备屏幕方向'
          }
        </div>
      </section>

      {/* 图片查看器测试 */}
      <section className={styles.section}>
        <h2>图片查看器测试</h2>
        <p style={{ color: '#666', fontSize: '14px', marginBottom: '15px' }}>
          点击图片进入查看模式，然后点击横屏按钮体验不同的横屏实现方式
        </p>
        <SwiperImages
          images={testImages}
          height="250px"
          autoplay={false}
          showToolbar={true}
          showRotateButton={true}
          showScaleButtons={true}
          showFullscreenButton={true}
          showLandscapeButton={true}
          showViewerPlayButton={true}
          onImageClick={handleImageClick}
          className={styles.customSwiper}
        />
      </section>

      {/* H5模拟横屏专门测试 */}
      <section className={styles.section}>
        <h2>H5模拟横屏专门测试</h2>
        <p style={{ color: '#666', fontSize: '14px', marginBottom: '15px' }}>
          这个组件强制使用H5模拟模式，即使在支持原生API的环境中
        </p>
        <SwiperImages
          images={testImages.slice(0, 2)} // 只显示横向图片
          height="200px"
          autoplay={false}
          showToolbar={true}
          showRotateButton={false}
          showScaleButtons={false}
          showFullscreenButton={false}
          showLandscapeButton={true}
          showViewerPlayButton={false}
          onImageClick={handleImageClick}
        />
      </section>

      {/* 技术说明 */}
      <section className={styles.section}>
        <h2>技术实现说明</h2>
        <div className={styles.instructions}>
          <h3>🔧 实现原理</h3>
          <p><strong>原生模式 (NATIVE):</strong></p>
          <p>• 使用 Screen Orientation API</p>
          <p>• 真正改变设备的屏幕方向</p>
          <p>• 支持 screen.orientation.lock() 方法</p>
          <p>• 需要用户手势触发</p>
          
          <p><strong>H5模拟模式 (SIMULATE):</strong></p>
          <p>• 使用 CSS transform: rotate(90deg)</p>
          <p>• 创建全屏容器覆盖整个视口</p>
          <p>• 调整PhotoView组件的尺寸和位置</p>
          <p>• 不改变实际设备方向，只是视觉效果</p>
          
          <h3>📱 适用场景</h3>
          <p><strong>原生模式适用于:</strong></p>
          <p>• 独立浏览器环境</p>
          <p>• 支持屏幕方向API的现代浏览器</p>
          <p>• 需要真正横屏的场景</p>
          
          <p><strong>H5模拟模式适用于:</strong></p>
          <p>• App内WebView环境</p>
          <p>• 微信、支付宝等内置浏览器</p>
          <p>• 不支持原生API的环境</p>
          <p>• 需要兼容性的场景</p>
          
          <h3>⚠️ 注意事项</h3>
          <p>• H5模拟模式下，设备实际方向不变</p>
          <p>• 模拟模式可能影响其他页面元素</p>
          <p>• 退出查看模式时会自动恢复</p>
          <p>• 某些复杂布局可能需要额外适配</p>
        </div>
      </section>

      {/* 用户代理信息 */}
      <section className={styles.section}>
        <h2>用户代理信息</h2>
        <Card>
          <div style={{ fontSize: '12px', color: '#666', wordBreak: 'break-all' }}>
            {userAgent}
          </div>
        </Card>
      </section>
    </div>
  );
};

export default H5LandscapeDemo;
