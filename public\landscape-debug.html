<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>横屏模式调试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .debug-panel {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 10000;
            max-width: 300px;
        }
        
        .controls {
            margin-bottom: 20px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.active {
            background: #28a745;
        }
        
        /* 模拟PhotoView结构 */
        .PhotoView-Portal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0,0,0,0.9);
            z-index: 9999;
            display: none;
        }
        
        .PhotoView-Slider {
            width: 100%;
            height: 100%;
            position: relative;
        }
        
        .PhotoView-Slider__item {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .PhotoView__PhotoWrap {
            width: 80%;
            height: 80%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #fff;
        }
        
        .PhotoView__PhotoWrap img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        
        /* 横屏模式样式 - 从实际代码复制 */
        html.landscape-mode .PhotoView-Portal {
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            width: 100vh !important;
            height: 100vw !important;
            transform: translate(-50%, -50%) rotate(90deg) !important;
            transform-origin: center center !important;
            z-index: 9999 !important;
            background: #000 !important;
        }

        html.landscape-mode .PhotoView-Slider {
            width: 100% !important;
            height: 100% !important;
            position: relative !important;
        }

        html.landscape-mode .PhotoView-Slider__item {
            width: 100% !important;
            height: 100% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        html.landscape-mode .PhotoView__PhotoWrap {
            width: 100% !important;
            height: 100% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            transform: none !important;
            left: 0 !important;
            top: 0 !important;
            position: relative !important;
        }

        html.landscape-mode .PhotoView__PhotoWrap img {
            max-width: 100% !important;
            max-height: 100% !important;
            width: auto !important;
            height: auto !important;
            object-fit: contain !important;
            transform: none !important;
            position: static !important;
            left: auto !important;
            top: auto !important;
            margin: 0 auto !important;
            display: block !important;
        }

        html.landscape-mode .PhotoView__PhotoWrap[style] {
            width: 100% !important;
            height: 100% !important;
            transform: none !important;
            left: 0 !important;
            top: 0 !important;
            position: relative !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        html.landscape-mode .PhotoView__PhotoWrap img[style] {
            transform: none !important;
            position: static !important;
            left: auto !important;
            top: auto !important;
            width: auto !important;
            height: auto !important;
            max-width: 100% !important;
            max-height: 100% !important;
            object-fit: contain !important;
        }
    </style>
</head>
<body>
    <div class="debug-panel" id="debugPanel">
        <div><strong>横屏模式调试</strong></div>
        <div id="debugInfo">等待初始化...</div>
    </div>
    
    <div class="controls">
        <button class="btn" onclick="toggleViewer()">切换查看器</button>
        <button class="btn" onclick="toggleLandscape()">切换横屏</button>
        <button class="btn" onclick="addInlineStyles()">添加内联样式</button>
        <button class="btn" onclick="resetStyles()">重置样式</button>
        <button class="btn" onclick="updateDebugInfo()">更新信息</button>
    </div>
    
    <div class="PhotoView-Portal" id="photoViewer">
        <div class="PhotoView-Slider">
            <div class="PhotoView-Slider__item">
                <div class="PhotoView__PhotoWrap" id="photoWrap">
                    <img src="https://picsum.photos/800/600?random=1" alt="测试图片" id="testImage">
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let isViewerOpen = false;
        let isLandscapeMode = false;
        
        function toggleViewer() {
            const viewer = document.getElementById('photoViewer');
            isViewerOpen = !isViewerOpen;
            viewer.style.display = isViewerOpen ? 'block' : 'none';
            updateDebugInfo();
        }
        
        function toggleLandscape() {
            isLandscapeMode = !isLandscapeMode;
            if (isLandscapeMode) {
                document.documentElement.classList.add('landscape-mode');
            } else {
                document.documentElement.classList.remove('landscape-mode');
            }
            updateDebugInfo();
        }
        
        function addInlineStyles() {
            const photoWrap = document.getElementById('photoWrap');
            const img = document.getElementById('testImage');
            
            // 模拟PhotoView添加的内联样式
            photoWrap.style.transform = 'translate3d(100px, 50px, 0px) scale(1.2)';
            photoWrap.style.left = '20px';
            photoWrap.style.top = '30px';
            photoWrap.style.position = 'absolute';
            
            img.style.transform = 'scale(1.5) rotate(10deg)';
            img.style.position = 'absolute';
            img.style.left = '50px';
            img.style.top = '25px';
            
            updateDebugInfo();
        }
        
        function resetStyles() {
            const photoWrap = document.getElementById('photoWrap');
            const img = document.getElementById('testImage');
            
            // 重置内联样式
            photoWrap.style.transform = 'none';
            photoWrap.style.left = '0px';
            photoWrap.style.top = '0px';
            photoWrap.style.position = 'relative';
            
            img.style.transform = 'none';
            img.style.position = 'static';
            img.style.left = 'auto';
            img.style.top = 'auto';
            
            updateDebugInfo();
        }
        
        function updateDebugInfo() {
            const photoWrap = document.getElementById('photoWrap');
            const img = document.getElementById('testImage');
            const computedWrap = window.getComputedStyle(photoWrap);
            const computedImg = window.getComputedStyle(img);
            
            const info = `
                <div>查看器: ${isViewerOpen ? '✅ 开启' : '❌ 关闭'}</div>
                <div>横屏: ${isLandscapeMode ? '✅ 开启' : '❌ 关闭'}</div>
                <div>屏幕: ${window.innerWidth} × ${window.innerHeight}</div>
                <hr>
                <div><strong>PhotoWrap:</strong></div>
                <div>内联transform: ${photoWrap.style.transform || '无'}</div>
                <div>计算transform: ${computedWrap.transform}</div>
                <div>内联position: ${photoWrap.style.position || '无'}</div>
                <div>计算position: ${computedWrap.position}</div>
                <hr>
                <div><strong>Image:</strong></div>
                <div>内联transform: ${img.style.transform || '无'}</div>
                <div>计算transform: ${computedImg.transform}</div>
                <div>内联position: ${img.style.position || '无'}</div>
                <div>计算position: ${computedImg.position}</div>
                <div>显示尺寸: ${img.offsetWidth} × ${img.offsetHeight}</div>
            `;
            
            document.getElementById('debugInfo').innerHTML = info;
        }
        
        // 初始化
        updateDebugInfo();
        
        // 监听窗口变化
        window.addEventListener('resize', updateDebugInfo);
    </script>
</body>
</html>
