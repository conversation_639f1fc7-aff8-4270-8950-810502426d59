import React from 'react';
import SwiperImages, { ImageItem } from './index';

// 示例图片数据
const sampleImages: ImageItem[] = [
  {
    src: 'https://picsum.photos/800/600?random=1',
    thumbnail: 'https://picsum.photos/400/300?random=1',
    alt: '示例图片1',
  },
  {
    src: 'https://picsum.photos/800/600?random=2',
    thumbnail: 'https://picsum.photos/400/300?random=2',
    alt: '示例图片2',
  },
  {
    src: 'https://picsum.photos/800/600?random=3',
    thumbnail: 'https://picsum.photos/400/300?random=3',
    alt: '示例图片3',
  },
  {
    src: 'https://picsum.photos/800/600?random=4',
    thumbnail: 'https://picsum.photos/400/300?random=4',
    alt: '示例图片4',
  },
];

const SwiperImagesExample: React.FC = () => {
  const handleImageClick = (index: number, image: ImageItem) => {
    console.log('图片点击:', index, image);
  };

  const handleSwiperChange = (index: number) => {
    console.log('轮播切换到:', index);
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>自动播放轮播图片查看器示例</h2>
      
      {/* 基础用法 */}
      <div style={{ marginBottom: '30px' }}>
        <h3>基础用法（自动播放）</h3>
        <SwiperImages
          images={sampleImages}
          height="250px"
          onImageClick={handleImageClick}
          onChange={handleSwiperChange}
        />
      </div>

      {/* 自定义配置 */}
      <div style={{ marginBottom: '30px' }}>
        <h3>自定义配置（快速播放，不循环）</h3>
        <SwiperImages
          images={sampleImages}
          height="200px"
          autoplayInterval={1500}
          loop={false}
          pauseOnView={true}
          resumeOnExit={false}
          onImageClick={handleImageClick}
        />
      </div>

      {/* 手动控制 */}
      <div style={{ marginBottom: '30px' }}>
        <h3>手动控制（关闭自动播放）</h3>
        <SwiperImages
          images={sampleImages}
          height="180px"
          autoplay={false}
          showIndicator={true}
          onImageClick={handleImageClick}
        />
      </div>

      {/* 单张图片 */}
      <div style={{ marginBottom: '30px' }}>
        <h3>单张图片</h3>
        <SwiperImages
          images={[sampleImages[0]]}
          height="200px"
          onImageClick={handleImageClick}
        />
      </div>

      {/* 空状态 */}
      <div style={{ marginBottom: '30px' }}>
        <h3>空状态</h3>
        <SwiperImages
          images={[]}
          height="150px"
        />
      </div>
    </div>
  );
};

export default SwiperImagesExample;
